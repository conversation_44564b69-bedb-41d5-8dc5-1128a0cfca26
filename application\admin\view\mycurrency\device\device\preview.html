<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>台球杆柜子预览</em></div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">柜子信息</div>
                    <div class="panel-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <td width="30%">柜子名称</td>
                                    <td>{$row.title??''}</td>
                                </tr>
                                <tr>
                                    <td>设备编号</td>
                                    <td>{$row.number??''}</td>
                                </tr>
                                <tr>
                                    <td>序列号</td>
                                    <td>{$row.serial_number??''}</td>
                                </tr>
                                <tr>
                                    <td>在线状态</td>
                                    <td>{$row.is_online==1?'不在线':'在线'}</td>
                                </tr>
                                <tr>
                                    <td>故障状态</td>
                                    <td>{$row.is_fault==1?'无故障':'有故障'}</td>
                                </tr>
                                <tr>
                                    <td>格口数量</td>
                                    <td>{:count($latticeList)}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">柜子二维码</div>
                    <div class="panel-body text-center">
                        {if $row.qrcode_image}
                        <img src="{$row.qrcode_image}" style="max-width:100%;" alt="柜子二维码">
                        {else}
                        <p>暂无二维码</p>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">台球杆柜子可视化</div>
                    <div class="panel-body">
                        <div class="locker-cabinet">
                            {foreach $latticeList as $index=>$lattice}
                            <div class="locker-door {$lattice.use_status==2?'empty':'has-cue'} {$lattice.is_fault==2?'fault':''}" data-number="{$lattice.number}">
                                <div class="locker-door-number">{$lattice.number}</div>
                                <div class="locker-door-content">
                                    {if $lattice.use_status == 1}
                                    <div class="cue-stick"></div>
                                    {else}
                                    <div class="empty-message">无杆</div>
                                    {/if}
                                </div>
                                <div class="locker-door-status">
                                    <div class="door-status-indicators">
                                        {if $lattice.door_state == 1}
                                        <span class="label label-warning">门未关</span>
                                        {else}
                                        <span class="label label-success">门已关</span>
                                        {/if}
                                        {if $lattice.is_fault == 2}
                                        <span class="label label-danger">故障</span>
                                        {/if}
                                    </div>
                                    <button type="button" class="btn btn-xs btn-success btn-open-door" data-lattice-id="{$lattice.id??''}">
                                        <i class="fa fa-unlock"></i> 开门
                                    </button>
                                </div>
                            </div>
                            {/foreach}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 柜子整体样式 */
.locker-cabinet {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    padding: 20px;
    background: #f0f0f0;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 格口门样式 */
.locker-door {
    position: relative;
    height: 200px;
    background: #e0e0e0;
    border: 2px solid #999;
    border-radius: 5px;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.locker-door:hover {
    transform: translateY(-5px);
    box-shadow: 2px 7px 10px rgba(0, 0, 0, 0.2);
}

/* 格口门编号 */
.locker-door-number {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 25px;
    height: 25px;
    background: #fff;
    border: 1px solid #999;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    z-index: 2;
}

/* 格口内容区域 */
.locker-door-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    min-height: 120px; /* 确保内容区域有最小高度 */
    max-height: 140px; /* 限制最大高度，为状态区域留出空间 */
    overflow: hidden; /* 防止内容溢出 */
}

/* 格口状态区域 */
.locker-door-status {
    min-height: 60px; /* 确保状态区域有固定高度 */
    padding: 5px;
    background: #f8f8f8;
    border-top: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* 状态指示器容器 */
.door-status-indicators {
    margin-bottom: 5px;
    min-height: 20px; /* 确保即使没有指示器也占用空间 */
}

/* 开门按钮样式 */
.btn-open-door {
    margin-top: 2px;
    width: 100%;
    font-weight: bold;
    background-color: #28a745;
    border-color: #28a745;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-open-door:hover {
    background-color: #218838;
    border-color: #1e7e34;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 台球杆样式 */
.cue-stick {
    width: 10px;
    height: 120px; /* 稍微减小高度 */
    background: linear-gradient(to bottom, 
        #8B4513 0%, /* 深棕色握把 */
        #8B4513 10%, 
        #D2B48C 10%, /* 浅棕色杆身 */
        #D2B48C 90%,
        #1E90FF 90%, /* 蓝色杆头 */
        #1E90FF 100%);
    border-radius: 5px;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    position: relative;
}

.cue-stick:before {
    content: '';
    position: absolute;
    top: -5px;
    left: 2px;
    width: 6px;
    height: 6px;
    background: #333;
    border-radius: 50%;
}

/* 空格口样式 */
.empty-message {
    color: #999;
    font-style: italic;
}

/* 有台球杆的格口 */
.has-cue {
    background: #e8f4f8;
}

/* 故障格口 */
.fault {
    background: #ffeded;
    border-color: #f88;
}
</style> 