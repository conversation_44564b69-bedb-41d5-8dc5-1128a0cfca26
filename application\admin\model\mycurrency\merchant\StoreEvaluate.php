<?php

namespace app\admin\model\mycurrency\merchant;

use think\Model;
use traits\model\SoftDelete;

class StoreEvaluate extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_merchant_store_evaluate';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'display_status_text'
    ];
    

    
    public function getDisplayStatusList()
    {
        return ['1' => __('Display_status 1'), '2' => __('Display_status 2')];
    }


    public function getDisplayStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['display_status']) ? $data['display_status'] : '');
        $list = $this->getDisplayStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function store()
    {
        return $this->belongsTo('Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
