<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh')}
                        <a class="btn btn-info btn-multi btn-disabled disabled {:$auth->check('mycurrency/agent/moneywithdrawal/restore')?'':'hide'}" href="javascript:;" data-url="mycurrency/agent/moneywithdrawal/restore" data-action="restore"><i class="fa fa-rotate-left"></i> {:__('Restore')}</a>
                        <a class="btn btn-danger btn-multi btn-disabled disabled {:$auth->check('mycurrency/agent/moneywithdrawal/destroy')?'':'hide'}" href="javascript:;" data-url="mycurrency/agent/moneywithdrawal/destroy" data-action="destroy"><i class="fa fa-times"></i> {:__('Destroy')}</a>
                        <a class="btn btn-success btn-restoreall {:$auth->check('mycurrency/agent/moneywithdrawal/restore')?'':'hide'}" href="javascript:;" data-url="mycurrency/agent/moneywithdrawal/restore" title="{:__('Restore all')}"><i class="fa fa-rotate-left"></i> {:__('Restore all')}</a>
                        <a class="btn btn-danger btn-destroyall {:$auth->check('mycurrency/agent/moneywithdrawal/destroy')?'':'hide'}" href="javascript:;" data-url="mycurrency/agent/moneywithdrawal/destroy" title="{:__('Destroy all')}"><i class="fa fa-times"></i> {:__('Destroy all')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                           data-operate-restore="{:$auth->check('mycurrency/agent/moneywithdrawal/restore')}"
                           data-operate-destroy="{:$auth->check('mycurrency/agent/moneywithdrawal/destroy')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
