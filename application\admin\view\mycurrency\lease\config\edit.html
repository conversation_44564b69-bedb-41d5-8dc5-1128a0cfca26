<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Is_credit')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->

<!--            <select  id="c-is_credit" class="form-control selectpicker" name="row[is_credit]">-->
<!--                {foreach name="isCreditList" item="vo"}-->
<!--                    <option value="{$key}" {in name="key" value="$row.is_credit"}selected{/in}>{$vo}</option>-->
<!--                {/foreach}-->
<!--            </select>-->

<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Is_deposit')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->

<!--            <select  id="c-is_deposit" class="form-control selectpicker" name="row[is_deposit]">-->
<!--                {foreach name="isDepositList" item="vo"}-->
<!--                <option value="{$key}" {in name="key" value="$row.is_deposit"}selected{/in}>{$vo}</option>-->
<!--                {/foreach}-->
<!--            </select>-->

<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Is_deposit_deduction')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->

<!--            <select  id="c-is_deposit_deduction" class="form-control selectpicker" name="row[is_deposit_deduction]">-->
<!--                {foreach name="isDepositDeductionList" item="vo"}-->
<!--                <option value="{$key}" {in name="key" value="$row.is_deposit"}selected{/in}>{$vo}</option>-->
<!--                {/foreach}-->
<!--            </select>-->

<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Deposit')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-deposit" class="form-control" step="0.01" name="row[deposit]" type="number" value="{$row.deposit|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_opportunity')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->

<!--            <select  id="c-pay_opportunity" class="form-control selectpicker" name="row[pay_opportunity]">-->
<!--                {foreach name="payOpportunityList" item="vo"}-->
<!--                <option value="{$key}" {in name="key" value="$row.is_deposit"}selected{/in}>{$vo}</option>-->
<!--                {/foreach}-->
<!--            </select>-->

<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rule_rxplain')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-rule_rxplain" class="form-control editor" rows="5" name="row[rule_rxplain]" cols="50">{$row.rule_rxplain|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
