<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_deposit')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_deposit" class="form-control selectpicker" name="row[is_deposit]">
                {foreach name="isDepositList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deposit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deposit" class="form-control" step="0.01" name="row[deposit]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rule_rxplain')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-rule_rxplain" class="form-control " rows="5" name="row[rule_rxplain]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
