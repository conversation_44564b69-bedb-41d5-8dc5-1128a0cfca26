<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Device_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-device_id" data-rule="required" data-source="mycurrency/device/index" class="form-control selectpage" name="row[device_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-number" class="form-control" name="row[number]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Use_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="useStatusList" item="vo"}
            <label for="row[use_status]-{$key}"><input id="row[use_status]-{$key}" name="row[use_status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_fault')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_fault" class="form-control selectpicker" name="row[is_fault]">
                {foreach name="isFaultList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_goods_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_goods_id" data-rule="required" data-source="billiard/store/goods/index" class="form-control selectpage" name="row[store_goods_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Usetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-usetime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[usetime]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
