<?php

namespace app\admin\model\mycurrency\operate\distribution;

use think\Model;
use traits\model\SoftDelete;

class Profit extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_operate_distribution_profit';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'cancell_type_text',
        'addtotime_text',
        'canceltime_text',
        'divideintotime_text'
    ];
    

    
    public function getStatusList()
    {
        return ['-1' => __('Status -1'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3')];
    }

    public function getCancellTypeList()
    {
        return ['1' => __('Cancell_type 1'), '2' => __('Cancell_type 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getCancellTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['cancell_type']) ? $data['cancell_type'] : '');
        $list = $this->getCancellTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getAddtotimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['addtotime']) ? $data['addtotime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getCanceltimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['canceltime']) ? $data['canceltime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getDivideintotimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['divideintotime']) ? $data['divideintotime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setAddtotimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setCanceltimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setDivideintotimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'distribution_user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
