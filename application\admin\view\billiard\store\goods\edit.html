<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input data-field='store_name' id="c-store_id" data-rule="required" data-source="mycurrency/merchant/store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goods_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input data-field="title" id="c-goods_id" data-rule="required" data-source="billiard/goods/goods/index" class="form-control selectpage" name="row[goods_id]" type="text" value="{$row.goods_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('收费时间(分)')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="time" class="form-control" name="row[duration]" type="number" value="{$row.strategy.duration|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('收费金额(元)')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unit_price" class="form-control" step="0.01" name="row[unit_price]" type="number" value="{$row.strategy.unit_price|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Advance_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-advance_fee" class="form-control" step="0.01" name="row[advance_fee]" type="number" value="{$row.advance_fee|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

