<?php

namespace app\admin\model\mycurrency\agent;

use think\Model;
use traits\model\SoftDelete;

class Agent extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_agent';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text'
    ];


    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }

    public function getIsIdentityList()
    {
        return ['1' => __('Is_identity 1'), '2' => __('Is_identity 2')];
    }

    public function getLevelList()
    {
        return ['1' => __('Level 1'), '2' => __('Level 2'), '3' => __('Level 3')];
    }

    public function getRoleTyopeList()
    {
        return ['1' => __('Role_type 1'), '2' => __('Role_type 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function agencyregion()
    {
        return $this->belongsTo('app\common\model\Area', 'agency_region_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
