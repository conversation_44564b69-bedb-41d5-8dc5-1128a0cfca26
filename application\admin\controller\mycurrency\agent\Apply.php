<?php

namespace app\admin\controller\mycurrency\agent;

use app\common\controller\Backend;
use app\common\model\mycurrency\AgentApply;
use app\common\model\mycurrency\AgentInformation;
use app\common\model\mycurrency\AgentProfit;
use think\Db;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 代理商-代理商申请管理
 *
 * @icon fa fa-circle-o
 */
class Apply extends Backend
{

    /**
     * Apply模型对象
     * @var \app\admin\model\mycurrency\agent\Apply
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\mycurrency\agent\Apply;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->assignconfig('toexamine', $this->auth->check('mycurrency/agent/apply/toexamine')); //审核权限
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','intention','fullname','phone','business_image','status','handle_explain','applytime','handletime','createtime','updatetime']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname','mobile']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 审核
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function toexamine($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $information = AgentInformation::where(['agent_apply_id' => $ids])->find();
            $this->view->assign('information', $information);
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            if($result == true && $params['status'] == AgentApply::STATUS_YITONGGUO){
                $agent_add = [
                    'user_id' => $row->user_id,
                    'number' => \app\common\model\mycurrency\Agent::getNumger(),
                    'fullname' => $row->fullname,
                    'phone' => $row->phone,
                    'money' => 0,
                    'status' => \app\common\model\mycurrency\Agent::STATUS_ZHENGCHENG,
                    'is_identity' => $row->is_identity,
                    'createtime' => time(),
                ];
                $agent = \app\common\model\mycurrency\Agent::create($agent_add);
                AgentInformation::where(['agent_apply_id' => $row->id])->update([
                    'agent_id' => $agent->id,
                ]);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

}
