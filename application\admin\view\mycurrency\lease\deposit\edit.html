<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" class="form-control" step="0.01" name="row[amount]" type="number" value="{$row.amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-pay_type" class="form-control selectpicker" name="row[pay_type]">
                {foreach name="payTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.pay_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-refund_type" class="form-control selectpicker" name="row[refund_type]">
                {foreach name="refundTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.refund_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_voucher')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_voucher" class="form-control" name="row[refund_voucher]" type="text" value="{$row.refund_voucher|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Place_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-place_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[place_time]" type="text" value="{:$row.place_time?datetime($row.place_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-payment_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[payment_time]" type="text" value="{:$row.payment_time?datetime($row.payment_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[refund_time]" type="text" value="{:$row.refund_time?datetime($row.refund_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_complete_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_complete_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[refund_complete_time]" type="text" value="{:$row.refund_complete_time?datetime($row.refund_complete_time):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
