<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {if condition="$row.payment_mode == 3"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_number" class="form-control" type="text" value="{$row.bank_number|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_opening')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_opening" class="form-control" type="text" value="{$row.bank_opening|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_account" class="form-control" type="text" value="{$row.bank_account|htmlentities}" readonly="readonly">
        </div>
    </div>
    {elseif condition="$row.payment_mode == 4"/}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wechat_code_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <ul class="row list-inline faupload-preview" id="p-wechat_code_image">
                <li class="col-xs-3">
                    <a href="{$row.wechat_code_image|htmlentities}" data-url="{$row.wechat_code_image|htmlentities}" target="_blank" class="thumbnail">
                        <img src="{$row.wechat_code_image|htmlentities}" onerror="this.src='/juhui_sys.php/ajax/icon?suffix=png';this.onerror=null;" class="img-responsive">
                    </a>
                </li>
            </ul>
        </div>
    </div>
    {else /}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Alipay_code_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <ul class="row list-inline faupload-preview" id="p-wechat_code_image">
                <li class="col-xs-3">
                    <a href="{$row.alipay_code_image|htmlentities}" data-url="{$row.alipay_code_image|htmlentities}" target="_blank" class="thumbnail">
                        <img src="{$row.alipay_code_image|htmlentities}" onerror="this.src='/juhui_sys.php/ajax/icon?suffix=png';this.onerror=null;" class="img-responsive">
                    </a>
                </li>
            </ul>
        </div>
    </div>
    {/if}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Offline_explain')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-offline_explain" class="form-control" name="row[offline_explain]" type="text" value="{$row.offline_explain|htmlentities}">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Offline_credential_images')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input id="c-offline_credential_images" class="form-control" size="50" name="row[offline_credential_images]" type="text" value="">-->
<!--                <div class="input-group-addon no-border no-padding">-->
<!--                    <span><button type="button" id="faupload-offline_credential_images" class="btn btn-danger faupload" data-input-id="c-offline_credential_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-offline_credential_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                    <span><button type="button" id="fachoose-offline_credential_images" class="btn btn-primary fachoose" data-input-id="c-offline_credential_images" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                </div>-->
<!--                <span class="msg-box n-right" for="c-offline_credential_images"></span>-->
<!--            </div>-->
<!--            <ul class="row list-inline faupload-preview" id="p-offline_credential_images"></ul>-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Offline_credential_images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-offline_credential_images" class="form-control" size="50" name="row[offline_credential_images]" type="textarea" value="{$row.offline_credential_images|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-offline_credential_images" class="btn btn-danger faupload" data-input-id="c-offline_credential_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-offline_credential_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-offline_credential_images" class="btn btn-primary fachoose" data-input-id="c-offline_credential_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-offline_credential_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-offline_credential_images"></ul>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
