<?php

return [
    'Sn'                        => '订单编号',
    'User_id'                   => '用户id',
    'Deposit_id'                => '押金id',
    'Strategy_id'               => '计费策略id',
    'Strategy_types'            => '策略类型',
    'Strategy_types 1'          => '按时长计费',
    'Strategy_types 2'          => '按次数计费',
    'Strategy_types 3'          => '按套餐计费',
    'Strategy_package_id'       => '计费套餐id',
    'Strategy_duration'         => '计费周期',
    'Strategy_unit_price'       => '单价',
    'Limit_start_time'          => '限制使用开始时间',
    'Limit_end_time'            => '限制使用结束时间',
    'Strategy_free_duration'    => '免费试用时长（分）',
    'Strategy_free_frequency'   => '免费次数',
    'Strategy_free_cycle'       => '免费周期(天)',
    'Strategy_overtime_money'   => '租赁超时收费金额',
    'Strategy_overtime_company' => '租赁超时收费单位（如：每超时30分钟收费多少元）',
    'Lease_time'                => '开始租赁时间',
    'Return_time'               => '结束租赁时间',
    'Overtime_duration'         => '租赁超时时长（分）',
    'Rent_fee'                  => '合计需支付租金',
    'Overtime_fee'              => '归还超时需支付费用',
    'Paymen_required'           => '租赁费用',
    'Paid_dlready'              => '已支付金额',
    'Use_status'                => '使用状态',
    'Use_status -1'             => '已取消',
    'Use_status 1'              => '申请免押中',
    'Use_status 2'              => '支付押金中',
    'Use_status 3'              => '支付租赁费用中【先付后用】',
    'Use_status 4'              => '使用中',
    'Use_status 5'              => '支付租赁费用中【先用后付or补交费用',
    'Use_status 6'              => '已完成',

    'Use_status 402'              => '租赁中',
    'Use_status 404'              => '归还中',

    'Cancell_type'              => '订单取消类型',
    'Cancell_type 1'            => '用户主动取消',
    'Cancell_type 2'            => '设备租赁失败取消',
    'Pay_status'                => '支付状态',
    'Pay_status 1'              => '未支付',
    'Pay_status 2'              => '已支付',
    'Createtime'                => '创建时间',
    'Updatetime'                => '更新时间',
    'Deletetime'                => '删除时间',
    'User.nickname'             => '昵称',
    'User.mobile'               => '手机号',
    'Strategy.title'            => '策略标题',
    'Package.title'             => '套餐标题'
];
