<?php

namespace app\admin\model\mycurrency\lease;

use think\Model;
use traits\model\SoftDelete;

class Orderdevice extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_lease_order_device';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'use_start_time_text',
        'use_end_time_text',
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3'), '4' => __('Status 4')];
    }


    public function getUseStartTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_start_time']) ? $data['use_start_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUseEndTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_end_time']) ? $data['use_end_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setUseStartTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUseEndTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function order()
    {
        return $this->belongsTo('Order', 'lease_order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function device()
    {
        return $this->belongsTo('app\admin\model\mycurrency\device\Device', 'device_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
