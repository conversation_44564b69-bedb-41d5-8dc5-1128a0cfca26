<?php

namespace app\admin\model\mycurrency\lease;

use think\Model;
use traits\model\SoftDelete;

class Deposit extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_lease_deposit';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'pay_type_text',
        'status_text',
        'refund_type_text',
        'place_time_text',
        'payment_time_text',
        'refund_time_text',
        'refund_complete_time_text'
    ];
    

    
    public function getPayTypeList()
    {
        return ['1' => __('Pay_type 1'), '2' => __('Pay_type 2'), '3' => __('Pay_type 3')];
    }

    public function getStatusList()
    {
        return [
            '-1' => __('Status -1'),
            '-2' => __('Status -2'),
            '1' => __('Status 1'),
            '2' => __('Status 2'),
            '3' => __('Status 3'),
            '4' => __('Status 4'),
            '5' => __('Status 5'),
        ];
    }

    public function getRefundTypeList()
    {
        return ['1' => __('Refund_type 1'), '2' => __('Refund_type 2')];
    }


    public function getPayTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_type']) ? $data['pay_type'] : '');
        $list = $this->getPayTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getRefundTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['refund_type']) ? $data['refund_type'] : '');
        $list = $this->getRefundTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getPlaceTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['place_time']) ? $data['place_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getPaymentTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['payment_time']) ? $data['payment_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getRefundTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['refund_time']) ? $data['refund_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getRefundCompleteTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['refund_complete_time']) ? $data['refund_complete_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setPlaceTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setPaymentTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setRefundTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setRefundCompleteTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
