<?php

namespace app\admin\controller\mycurrency\merchant;

use app\admin\model\Area;
use app\common\controller\Backend;
use app\common\model\mycurrency\MerchantStaff;
use app\common\model\mycurrency\MerchantStore;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 商铺-门店管理
 *
 * @icon fa fa-circle-o
 */
class Store extends Backend
{

    /**
     * Store模型对象
     * @var \app\admin\model\mycurrency\merchant\Store
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\mycurrency\merchant\Store;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign('managementTypeList', [
            '1' =>'门店自营',
            '2' => '山屿自营'
        ]);
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {

        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['merchant','area','agent','admin'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','store_name','dorrhead_image','album_images','city_id','area_id','address','longitude','latitude','fullname','phone','money','score','status','createtime','updatetime','divideinto','management_type']);
                $row->visible(['merchant']);
				$row->getRelation('merchant')->visible(['name']);
                $row->visible(['admin']);
                $row->getRelation('admin')->visible(['nickname']);
				$row->visible(['area']);
				$row->getRelation('area')->visible(['name']);
                $row->visible(['agent']);
                $row->getRelation('agent')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        $admin = $this->request->post('admin/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            $result = MerchantStore::storeAdd($params,$admin,0);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $row['province_shortname'] = Area::where(['id' => $row['province_id']])->value('shortname');
            $row['city_shortname'] = Area::where(['id' => $row['city_id']])->value('shortname');
            $row['area_shortname'] = Area::where(['id' => $row['area_id']])->value('shortname');
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        $admin = $this->request->post('admin/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            $params['admin_id'] = $this->auth->id;
            $result = MerchantStore::storeUpdate($params,$ids);
            if($result == true){
                if ($admin['password'] != null){
                    $where = [];
                    $where['store_id'] = $row->id;
                    $staff = MerchantStaff::where($where)->find();
                    if ($staff){
                        $password = md5(md5($admin['password']) . $staff->salt);
//                        $staff->password = $password;
//                        $staff->save();
                        MerchantStaff::where(['store_id' => $row->id])->update([
                            'password' => $password,
                        ]);
                    }
                }
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

}
