<?php

namespace app\admin\model\mycurrency\agent;

use think\Model;


class Profit extends Model
{

    

    

    // 表名
    protected $name = 'mycurrency_agent_profit';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function agent()
    {
        return $this->belongsTo('app\admin\model\mycurrency\agent\Agent', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
