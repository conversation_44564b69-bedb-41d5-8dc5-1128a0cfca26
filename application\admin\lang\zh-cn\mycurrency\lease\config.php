<?php

return [
    'Is_deposit'   => '是否需要缴纳押金租赁 ',
    'Is_deposit 1' => '不需要缴纳',
    'Is_deposit 2' => '需要缴纳',
    'Deposit'      => '押金',
    'Rule_rxplain' => '租赁规则',

    'Is_deposit_deduction'   => '是否从押金中扣款',
    'Is_deposit_deduction 1' => '不使用',
    'Is_deposit_deduction 2' => '使用',

    'Pay_opportunity'   => '支付时机 ',
    'Pay_opportunity 1' => '先用后付',
    'Pay_opportunity 2' => '先付后用',

    'Is_credit'   => '是否使用免押租赁',
    'Is_credit 1' => '不使用',
    'Is_credit 2' => '使用',
];
