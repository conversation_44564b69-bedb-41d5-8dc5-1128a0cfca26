<?php

namespace app\admin\model\mycurrency\merchant;

use think\Model;
use traits\model\SoftDelete;

class Profit extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_merchant_profit';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'change_type_text'
    ];
    

    
    public function getChangeTypeList()
    {
        return ['1' => __('Change_type 1'), '2' => __('Change_type 2')];
    }


    public function getChangeTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['change_type']) ? $data['change_type'] : '');
        $list = $this->getChangeTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function merchant()
    {
        return $this->belongsTo('app\admin\model\mycurrency\Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function store()
    {
        return $this->belongsTo('Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
