<?php

namespace app\admin\model\mycurrency\lease;

use think\Model;


class Config extends Model
{

    

    

    // 表名
    protected $name = 'mycurrency_lease_config';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'is_deposit_text'
    ];
    

    
    public function getIsDepositList()
    {
        return ['1' => __('Is_deposit 1'), '2' => __('Is_deposit 2')];
    }

    public function getIsDepositDeductionList()
    {
        return ['1' => __('Is_deposit_deduction 1'), '2' => __('Is_deposit_deduction 2')];
    }

    public function getPayOpportunityList()
    {
        return ['1' => __('Pay_opportunity 1'), '2' => __('Pay_opportunity 2')];
    }

    public function getIsCreditList()
    {
        return ['1' => __('Is_credit 1'), '2' => __('Is_credit 2')];
    }


    public function getIsDepositTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_deposit']) ? $data['is_deposit'] : '');
        $list = $this->getIsDepositList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}
