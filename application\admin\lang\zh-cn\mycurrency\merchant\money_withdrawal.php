<?php

return [
    'Change_type'               => '提现类型',
    'Change_type 1'             => '商铺收益提现',
    'Change_type 2'             => '门店收益提现',
    'Merchant_id'               => '商铺ID',
    'Store_id'                  => '门店id',
    'Money'                     => '提现金额',
    'Witserpro'                 => '提现手续费比例（%）',
    'Service_charge'            => '手续费',
    'Actual_payment'            => '实际需打款金额',
    'Payment_mode'              => '提现方式',
    'Payment_mode 1'            => '微信打款',
    'Payment_mode 2'            => '支付宝打款',
    'Payment_mode 3'            => '银行卡线下打款',
    'Payment_mode 4'            => '微信收款码收款',
    'Payment_mode 5'            => '支付宝收款码收款',
    'Bank_number'               => '银行卡号',
    'Bank_opening'              => '开户行',
    'Bank_account'              => '开户人',
    'Explain'                   => '提现说明',
    'Status'                    => '状态',
    'Status -1'                 => '取消',
    'Set status to -1'          => '设为取消',
    'Status -2'                 => '提现驳回',
    'Set status to -2'          => '设为提现驳回',
    'Status 1'                  => '待处理',
    'Set status to 1'           => '设为待处理',
    'Status 2'                  => '审核通过待打款',
    'Set status to 2'           => '设为审核通过待打款',
    'Status 3'                  => '打款中',
    'Set status to 3'           => '设为打款中',
    'Status 4'                  => '已打款',
    'Set status to 4'           => '设为已打款',
    'Cancellation_type'         => '取消原因',
    'Cancellation_type 1'       => '用户取消提现',
    'Cancellation_type 2'       => '打款失败取消',
    'Operation_explain'         => '管理员操作说明',
    'Offline_explain'           => '线下打款说明',
    'Offline_credential_images' => '线下打款凭据',
    'Applytime'                 => '申请时间',
    'Rejecttime'                => '管理员驳回时间',
    'Toexaminttime'             => '管理员审核通过时间',
    'Paymenttime'               => '打款时间',
    'Createtime'                => '添加时间',
    'Updatetime'                => '更新时间',
    'Deletetime'                => '删除时间',
    'Merchant.name'             => '商铺名称',
    'Store.store_name'          => '门店名称',
    'Wechat_code_image' => '微信收款码',
    'Alipay_code_image' => '支付宝收款码',
];
