<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" min="0" data-source="mycurrency/agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Witserpro')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-witserpro" class="form-control" step="0.0001" name="row[witserpro]" type="number" value="{$row.witserpro|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_charge')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_charge" class="form-control" step="0.01" name="row[service_charge]" type="number" value="{$row.service_charge|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Actual_payment')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-actual_payment" class="form-control" step="0.01" name="row[actual_payment]" type="number" value="{$row.actual_payment|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_mode')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-payment_mode" class="form-control selectpicker" name="row[payment_mode]">
                {foreach name="paymentModeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.payment_mode"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Explain')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-explain" class="form-control" name="row[explain]" type="text" value="{$row.explain|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Operation_explain')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-operation_explain" class="form-control" name="row[operation_explain]" type="text" value="{$row.operation_explain|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Applytime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-applytime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[applytime]" type="text" value="{:$row.applytime?datetime($row.applytime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rejecttime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rejecttime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[rejecttime]" type="text" value="{:$row.rejecttime?datetime($row.rejecttime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Toexaminttime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-toexaminttime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[toexaminttime]" type="text" value="{:$row.toexaminttime?datetime($row.toexaminttime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paymenttime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paymenttime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[paymenttime]" type="text" value="{:$row.paymenttime?datetime($row.paymenttime):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
