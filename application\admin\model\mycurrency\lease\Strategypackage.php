<?php

namespace app\admin\model\mycurrency\lease;

use think\Model;
use traits\model\SoftDelete;

class Strategypackage extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_lease_strategy_package';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function strategy()
    {
        return $this->belongsTo('Strategy', 'strategy_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
