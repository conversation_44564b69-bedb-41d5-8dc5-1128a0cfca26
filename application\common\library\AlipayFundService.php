<?php
// application/common/library/AlipayFundService.php

namespace app\common\library;

use think\Config;

/**
 * 支付宝资金授权(免押) All-in-One 业务工具类
 * 内部集成了客户端初始化和所有业务API的调用
 */
class AlipayFundService
{
    /**
     * 客户端实例的静态缓存
     * @var \AopCertClient
     */
    private static $client = null;

    // ===================================================================
    //  公开的业务方法 (给控制器调用)
    // ===================================================================

    /**
     * 创建芝麻免押资金授权冻结订单 (获取orderStr)
     * @param string $orderTitle        订单标题, 如 "台球杆租赁押金"
     * @param string $outOrderNo        商户订单号, 需唯一，仅支持字母、数字、下划线
     * @param string $outRequestNo      商户请求流水号, 用于标示请求流水的唯一性，可与out_order_no相同
     * @param float  $amount            需要冻结的金额, 单位元，取值范围：[0.01,100000000.00]  【弃用】
     * @param float  $hourlyRate        每小时租赁单价，用于生成计费说明  【弃用】
     * @param string $description       说明
     * @param string $itemName          租赁项目名称，如 "租金"，默认为 "租金"
     * @return string                   返回给App/小程序前端的orderStr
     * @throws \Exception
     */
    public static function createOrder($orderTitle, $outOrderNo, $outRequestNo = null, $description , $itemName = '租金')//$amount = 0.01, $hourlyRate = 2.0
    {
        require_once EXTEND_PATH . 'alipay/aop/request/AlipayFundAuthOrderAppFreezeRequest.php';

        $client = self::getClient(); // 使用内部方法获取客户端
        $request = new \AlipayFundAuthOrderAppFreezeRequest();
        // 读取配置
        $config = Config::get('alipaychapter');

        if (empty($config)) {
            throw new \Exception("支付宝配置[alipaychapter.php]不存在或为空");
        }

        // 如果没有传入请求流水号，则使用订单号
        if (empty($outRequestNo)) {
            $outRequestNo = $outOrderNo;
        }

        // 获取最大支付金额
        $maxPayAmount = $config['max_pay_amount'] ?? 99;

        // 构建后付费项目
        $postPayments = [
            [
                'name' => $itemName,
                'description' => $description,//"{$hourlyRate}元/小时，{$maxPayAmount}元封顶"
                ]
        ];

        // 构建芝麻免押必需的extra_param参数
        $extraParam = [
            'category' => $config['category'],
            'serviceId' => $config['service_id'] ?? '2025053000000000000109982700',
        ];

        // 构建业务参数
        $bizContent = [
            'out_order_no'   => $outOrderNo,
            'out_request_no' => $outRequestNo,
            'order_title'    => $orderTitle,
            'amount'         => $maxPayAmount,//$amount,
            'product_code'   => 'PRE_AUTH_ONLINE',
            'deposit_product_mode'=>'POSTPAY_UNCERTAIN',
            'extra_param'    => json_encode($extraParam),
            'post_payments'  => json_encode($postPayments)
        ];

        $request->setBizContent(json_encode($bizContent));
        $request->setNotifyUrl($config['notify_url']);

        return $client->sdkExecute($request);
    }

    /**
     * 撤销资金授权订单 (用于硬件故障等无法提供服务时取消订单，必须24小时内调用)
     * @param string $remark           商户对本次撤销操作的附言描述，长度不超过100个字母或50个汉字
     * @param string $outRequestNo     商户的授权资金操作流水号，与资金冻结时out_request_no一致
     * @param string $outOrderNo       商户的授权资金订单号，与资金冻结时out_order_no一致
     * @return array                   返回撤销结果数组
     * @throws \Exception
     */
    public static function cancelAuthOrder($remark, $outRequestNo, $outOrderNo = '')
    {
        require_once EXTEND_PATH . 'alipay/aop/request/AlipayFundAuthOperationCancelRequest.php';

        $client = self::getClient(); // 使用内部方法获取客户端
        $request = new \AlipayFundAuthOperationCancelRequest();

        // 构建业务参数
        $bizContent = [
            'remark' => $remark,
            'out_request_no' => $outRequestNo,
            'out_order_no'=>$outOrderNo
        ];

        $request->setBizContent(json_encode($bizContent));

        try {
            $response = $client->execute($request);
            $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";

            if (!empty($response->$responseNode->code) && $response->$responseNode->code == '10000') {
                // 撤销成功，返回业务数据
                return [
                    'success' => true,
                    'code' => $response->$responseNode->code,
                    'msg' => $response->$responseNode->msg ?? '撤销成功',
                    'data' => [
                        'out_order_no' => $response->$responseNode->out_order_no ?? '',
                        'out_request_no' => $response->$responseNode->out_request_no ?? '',
                        'auth_no' => $response->$responseNode->auth_no ?? '',
                        'operation_id' => $response->$responseNode->operation_id ?? '',
                        'action' => $response->$responseNode->action ?? ''
                    ]
                ];

//
//                {
//                                        "code": 1,
//                    "msg": "资金授权撤销成功",
//                    "time": "1749878510",
//                    "data": {
//                                        "cancel_result": {
//                                            "success": true,
//                    "code": "10000",
//                    "msg": "Success",
//                    "data": {
//                                                "out_order_no": "80777352559380240",
//                    "out_request_no": "80777352559380240",
//                    "auth_no": "2025061410002001770576235319",
//                    "operation_id": "20250614388239837705",
//                    "action": "unfreeze"
//                    }
//                    },
//                    "test_params": {
//                                            "remark": "硬件故障，无法提供服务，撤销授权",
//                    "out_request_no": "80777352559380240",
//                    "out_order_no": "80777352559380240"
//                    }
//                    }
//               }
            } else {
                // 撤销失败
                return [
                    'success' => false,
                    'code' => $response->$responseNode->code ?? 'UNKNOWN_ERROR',
                    'msg' => $response->$responseNode->msg ?? '撤销失败',
                    'sub_code' => $response->$responseNode->sub_code ?? '',
                    'sub_msg' => $response->$responseNode->sub_msg ?? ''
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception("资金授权撤销失败: " . $e->getMessage());
        }
    }

    /**
     * 查询资金授权操作详情
     * @param string $outOrderNo       商户的授权资金订单号
     * @param string $outRequestNo     商户的授权资金操作流水号
     * @param string $operationType    需要查询的授权资金操作类型(可选): FREEZE-冻结, UNFREEZE-解冻, PAY-支付
     * @return array                   返回查询结果数组
     * @throws \Exception
     */
    public static function queryAuthOrderDetail($outOrderNo = '', $outRequestNo = '', $operationType = '')
    {
        require_once EXTEND_PATH . 'alipay/aop/request/AlipayFundAuthOperationDetailQueryRequest.php';

        $client = self::getClient(); // 使用内部方法获取客户端
        $request = new \AlipayFundAuthOperationDetailQueryRequest();

        // 构建业务参数
        $bizContent = [];
        // 添加订单号参数
            $bizContent['out_order_no'] = $outOrderNo;
        // 添加请求流水号参数
            $bizContent['out_request_no'] = $outRequestNo;
        // 添加操作类型参数
        if (!empty($operationType)) {
            // 验证操作类型是否合法
            $validTypes = ['FREEZE', 'UNFREEZE', 'PAY'];
            if (!in_array(strtoupper($operationType), $validTypes)) {
                throw new \Exception("无效的操作类型: {$operationType}，支持的类型: " . implode(', ', $validTypes));
            }
            $bizContent['operation_type'] = strtoupper($operationType);
        }

        // 至少需要提供一个查询条件
        if (empty($outOrderNo) || empty($outRequestNo)) {
            throw new \Exception("查询条件不能为空，需要提供 out_order_no 和 out_request_no 2个参数");
        }

        $request->setBizContent(json_encode($bizContent));

        try {
            $response = $client->execute($request);
            $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";

            if (!empty($response->$responseNode->code) && $response->$responseNode->code == '10000') {
                // 查询成功，返回详细数据
                $data = $response->$responseNode;
                return [
                    'success' => true,
                    'code' => $data->code,
                    'msg' => $data->msg ?? '查询成功',
                    'data' => [
                        // 基本信息
                        'auth_no' => $data->auth_no ?? '',
                        'out_order_no' => $data->out_order_no ?? '',
                        'order_status' => $data->order_status ?? '',
                        'order_title' => $data->order_title ?? '',

                        // 金额信息
                        'total_freeze_amount' => $data->total_freeze_amount ?? '0.00',
                        'rest_amount' => $data->rest_amount ?? '0.00',
                        'total_pay_amount' => $data->total_pay_amount ?? '0.00',
                        'amount' => $data->amount ?? '0.00',

                        // 操作信息
                        'operation_id' => $data->operation_id ?? '',
                        'out_request_no' => $data->out_request_no ?? '',
                        'operation_type' => $data->operation_type ?? '',
                        'status' => $data->status ?? '',
                        'remark' => $data->remark ?? '',

                        // 用户信息
                        'payer_logon_id' => $data->payer_logon_id ?? '',
                        'payer_user_id' => $data->payer_user_id ?? '',
                        'payer_open_id' => $data->payer_open_id ?? '',

                        // 时间信息
                        'gmt_create' => $data->gmt_create ?? '',
                        'gmt_trans' => $data->gmt_trans ?? '',

                        // 扩展信息
                        'extra_param' => $data->extra_param ?? '',
                        'pre_auth_type' => $data->pre_auth_type ?? '',
                        'trans_currency' => $data->trans_currency ?? '',

                        // 详细金额信息
                        'total_freeze_credit_amount' => $data->total_freeze_credit_amount ?? '0.00',
                        'total_freeze_fund_amount' => $data->total_freeze_fund_amount ?? '0.00',
                        'total_pay_credit_amount' => $data->total_pay_credit_amount ?? '0.00',
                        'total_pay_fund_amount' => $data->total_pay_fund_amount ?? '0.00',
                        'rest_credit_amount' => $data->rest_credit_amount ?? '0.00',
                        'rest_fund_amount' => $data->rest_fund_amount ?? '0.00',
                        'credit_amount' => $data->credit_amount ?? '0.00',
                        'fund_amount' => $data->fund_amount ?? '0.00',
                        'credit_merchant_ext' => $data->credit_merchant_ext ?? ''
                    ]
                ];
            } else {
                // 查询失败
                return [
                    'success' => false,
                    'code' => $response->$responseNode->code ?? 'UNKNOWN_ERROR',
                    'msg' => $response->$responseNode->msg ?? '查询失败',
                    'sub_code' => $response->$responseNode->sub_code ?? '',
                    'sub_msg' => $response->$responseNode->sub_msg ?? ''
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception("资金授权详情查询失败: " . $e->getMessage());
        }
    }

    /**
     * 资金授权解冻接口
     * @param string $authNo           支付宝资金授权订单号(必选)
     * @param string $outRequestNo     解冻请求流水号(必选)  特别注意:这里的流水号一定要重新生成,绝对不能用数据库里存的流水号
     * @param float  $amount           本次操作解冻的金额，单位为元(必选)
     * @param string $remark           商户对本次解冻操作的附言描述(必选)
     * @return array                   返回解冻结果数组
     * @throws \Exception
     */
    public static function unfreezeAuthOrder($authNo, $outRequestNo, $amount, $remark)
    {
        require_once EXTEND_PATH . 'alipay/aop/request/AlipayFundAuthOrderUnfreezeRequest.php';

        $client = self::getClient(); // 使用内部方法获取客户端
        $request = new \AlipayFundAuthOrderUnfreezeRequest();

        // 读取配置
        $config = Config::get('alipaychapter');

        $request->setNotifyUrl($config['unfreeze_notify_url']); //设置解冻免押订单的回调

        // 参数验证
        if (empty($authNo)) {
            throw new \Exception("支付宝资金授权订单号(auth_no)不能为空");
        }
        if (empty($outRequestNo)) {
            throw new \Exception("解冻请求流水号(out_request_no)不能为空");
        }
        if (empty($amount) || $amount <= 0) {
            throw new \Exception("解冻金额必须大于0");
        }
        if (empty($remark)) {
            throw new \Exception("解冻操作附言描述(remark)不能为空");
        }

        // 金额范围验证
        if ($amount < 0.01 || $amount > 100000000.00) {
            throw new \Exception("解冻金额超出范围，取值范围：[0.01,100000000.00]");
        }

        // 构建业务参数
        $bizContent = [
            'auth_no' => $authNo,
            'out_request_no' => $outRequestNo,
            'amount' => number_format($amount, 2, '.', ''),
            'remark' => $remark,
//            'extra_param' => json_encode(['unfreezeBizInfo'=>['bizComplete'=>'true']])
        ];
        $request->setBizContent(json_encode($bizContent));

        try {
            $response = $client->execute($request);
            $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";

            if (!empty($response->$responseNode->code) && $response->$responseNode->code == '10000') {
                // 解冻成功，返回业务数据
                $data = $response->$responseNode;
                return [
                    'success' => true,
                    'code' => $data->code,
                    'msg' => $data->msg ?? '解冻成功',
                    'data' => [
                        'auth_no' => $data->auth_no ?? '',
                        'out_order_no' => $data->out_order_no ?? '',
                        'operation_id' => $data->operation_id ?? '',
                        'out_request_no' => $data->out_request_no ?? '',
                        'amount' => $data->amount ?? '0.00',
                        'status' => $data->status ?? '',
                        'gmt_trans' => $data->gmt_trans ?? '',
                        'credit_amount' => $data->credit_amount ?? '0.00',
                        'fund_amount' => $data->fund_amount ?? '0.00'
                    ]
                ];
            } else {
                // 解冻失败
                return [
                    'success' => false,
                    'code' => $response->$responseNode->code ?? 'UNKNOWN_ERROR',
                    'msg' => $response->$responseNode->msg ?? '解冻失败',
                    'sub_code' => $response->$responseNode->sub_code ?? '',
                    'sub_msg' => $response->$responseNode->sub_msg ?? ''
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception("资金授权解冻失败: " . $e->getMessage());
        }
    }

    /**
     * 统一收单交易支付接口（预授权转支付/扣款）
     * 用户在商户侧已授权下单并享受服务后，商户使用授权单号通过本接口对用户已授权金额发起扣款
     *
     * @param string $outTradeNo        商户订单号，由商家自定义，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复(必选)
     * @param float  $totalAmount       订单总金额，单位为元，精确到小数点后两位，取值范围：[0.01,100000000](必选)
     * @param string $subject           订单标题，注意：不可使用特殊字符，如 /，=，& 等(必选)
     * @param string $authNo            资金预授权单号，支付宝预授权和新当面资金授权场景下必填(可选，但预授权场景必填)
     * @param string $productCode       商家和支付宝签约的产品码，预授权场景固定传PREAUTH_PAY(可选，默认PREAUTH_PAY)
     * @param string $authConfirmMode   预授权确认模式：COMPLETE-转交易完成后解冻剩余冻结金额，NOT_COMPLETE-转交易完成后不解冻剩余冻结金额(可选，默认COMPLETE)
     * @return array                    返回扣款结果数组
     * @throws \Exception
     */
    public static function payWithAuth($outTradeNo, $totalAmount, $subject, $authNo = '', $productCode = 'PREAUTH_PAY', $authConfirmMode = 'COMPLETE')
    {
        require_once EXTEND_PATH . 'alipay/aop/request/AlipayTradePayRequest.php';

        $client = self::getClient(); // 使用内部方法获取客户端
        $request = new \AlipayTradePayRequest();
        $config = Config::get('alipaychapter');
        $request->setNotifyUrl($config['pay_notify_url']);
        // 参数验证
        if (empty($outTradeNo)) {
            throw new \Exception("商户订单号(out_trade_no)不能为空");
        }
        if (empty($totalAmount) || $totalAmount <= 0) {
            throw new \Exception("订单总金额必须大于0");
        }
        if (empty($subject)) {
            throw new \Exception("订单标题(subject)不能为空");
        }

        // 金额范围验证
        if ($totalAmount < 0.01 || $totalAmount > 100000000) {
            throw new \Exception("订单金额超出范围，取值范围：[0.01,100000000]");
        }

        // 验证预授权确认模式
        $validAuthConfirmModes = ['COMPLETE', 'NOT_COMPLETE'];
        if (!in_array($authConfirmMode, $validAuthConfirmModes)) {
            throw new \Exception("无效的预授权确认模式: {$authConfirmMode}，支持的模式: " . implode(', ', $validAuthConfirmModes));
        }

        // 构建业务参数
        $bizContent = [
            'out_trade_no' => $outTradeNo,                                    // 商户订单号
            'total_amount' => number_format($totalAmount, 2, '.', ''),       // 订单总金额，确保格式正确
            'subject' => $subject,                                            // 订单标题
            'product_code' => $productCode                                    // 产品码，预授权场景固定PREAUTH_PAY
        ];

        // 如果提供了授权单号，添加预授权相关参数
        if (!empty($authNo)) {
            $bizContent['auth_no'] = $authNo;                                // 资金预授权单号
            $bizContent['auth_confirm_mode'] = $authConfirmMode;             // 预授权确认模式
        }

        $request->setBizContent(json_encode($bizContent));

        try {
            $response = $client->execute($request);
            $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";

            if (!empty($response->$responseNode->code) && $response->$responseNode->code == '10000') {
                // 扣款成功，返回业务数据
                $data = $response->$responseNode;
                return [
                    'success' => true,
                    'code' => $data->code,
                    'msg' => $data->msg ?? '扣款成功',
                    'data' => [
                        // 基本交易信息
                        'trade_no' => $data->trade_no ?? '',                 // 支付宝交易号
                        'out_trade_no' => $data->out_trade_no ?? '',         // 商户订单号
                        'total_amount' => $data->total_amount ?? '0.00',     // 交易总金额
                        'receipt_amount' => $data->receipt_amount ?? '0.00', // 实收金额
                        'buyer_pay_amount' => $data->buyer_pay_amount ?? '0.00', // 买家实付金额

                        // 买家信息
                        'buyer_logon_id' => $data->buyer_logon_id ?? '',     // 买家支付宝账号
                        'buyer_user_id' => $data->buyer_user_id ?? '',       // 买家用户ID
                        'buyer_open_id' => $data->buyer_open_id ?? '',       // 买家OpenID

                        // 交易时间
                        'gmt_payment' => $data->gmt_payment ?? '',           // 交易付款时间

                        // 预授权相关信息
                        'auth_trade_pay_mode' => $data->auth_trade_pay_mode ?? '', // 预授权支付模式
                        'async_payment_mode' => $data->async_payment_mode ?? '',   // 异步支付模式

                        // 金额明细
                        'point_amount' => $data->point_amount ?? '0.00',     // 积分支付金额
                        'invoice_amount' => $data->invoice_amount ?? '0.00', // 可开发票金额
                        'discount_amount' => $data->discount_amount ?? '0.00', // 平台优惠金额
                        'mdiscount_amount' => $data->mdiscount_amount ?? '0.00', // 商家优惠金额

                        // 资金明细列表
                        'fund_bill_list' => $data->fund_bill_list ?? [],     // 支付金额信息

                        // 优惠券明细
                        'voucher_detail_list' => $data->voucher_detail_list ?? [], // 优惠券详情
                        'discount_goods_detail' => $data->discount_goods_detail ?? '', // 优惠商品明细

                        // 门店信息
                        'store_name' => $data->store_name ?? '',             // 门店名称
                        'store_id' => $data->store_id ?? '',                 // 商户门店编号
                        'terminal_id' => $data->terminal_id ?? '',           // 商户机具终端编号

                        // 其他扩展信息
                        'send_pay_date' => $data->send_pay_date ?? '',       // 发送支付日期
                        'card_balance' => $data->card_balance ?? '0.00',     // 卡余额
                        'discountable_amount' => $data->discountable_amount ?? '0.00', // 可打折金额
                        'undiscountable_amount' => $data->undiscountable_amount ?? '0.00' // 不可打折金额
                    ]
                ];
            } else {
                // 扣款失败
                return [
                    'success' => false,
                    'code' => $response->$responseNode->code ?? 'UNKNOWN_ERROR',
                    'msg' => $response->$responseNode->msg ?? '扣款失败',
                    'sub_code' => $response->$responseNode->sub_code ?? '',
                    'sub_msg' => $response->$responseNode->sub_msg ?? ''
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception("预授权转支付失败: " . $e->getMessage());
        }
    }

    /**
     * 统一收单交易查询接口
     * 该接口提供所有支付宝支付订单的查询，商户可以通过该接口主动查询订单状态，完成下一步的业务逻辑
     *
     * @param string $outTradeNo        订单支付时传入的商户订单号，和支付宝交易号不能同时为空(可选，但与trade_no必须提供其一)
     * @return array                    返回查询结果数组
     * @throws \Exception
     */
    public static function queryTrade($outTradeNo = '')
    {
        require_once EXTEND_PATH . 'alipay/aop/request/AlipayTradeQueryRequest.php';

        $client = self::getClient(); // 使用内部方法获取客户端
        $request = new \AlipayTradeQueryRequest();

        // 参数验证：至少需要提供一个查询条件
        if (empty($outTradeNo)) {
            throw new \Exception("商户订单号(out_trade_no)不能为空，至少需要提供其中一个");
        }
        // 构建业务参数
        $bizContent = [];
        // 添加商户订单号（如果提供）
        if (!empty($outTradeNo)) {
            $bizContent['out_trade_no'] = $outTradeNo;
        }
        $request->setBizContent(json_encode($bizContent));
        try {
            $response = $client->execute($request);
            $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
            if (!empty($response->$responseNode->code) && $response->$responseNode->code == '10000') {
                // 查询成功，返回详细数据
                $data = $response->$responseNode;
                return [
                    'success' => true,
                    'code' => $data->code,
                    'msg' => $data->msg ?? '查询成功',
                    'data' => [
                        // 基本交易信息
                        'trade_no' => $data->trade_no ?? '',                 // 支付宝交易号
                        'out_trade_no' => $data->out_trade_no ?? '',         // 商户订单号
                        'trade_status' => $data->trade_status ?? '',         // 交易状态
                        'total_amount' => $data->total_amount ?? '0.00',     // 交易总金额
                        'receipt_amount' => $data->receipt_amount ?? '0.00', // 实收金额
                        'buyer_pay_amount' => $data->buyer_pay_amount ?? '0.00', // 买家实付金额

                        // 买家信息
                        'buyer_logon_id' => $data->buyer_logon_id ?? '',     // 买家支付宝账号
                        'buyer_user_id' => $data->buyer_user_id ?? '',       // 买家用户ID
                        'buyer_open_id' => $data->buyer_open_id ?? '',       // 买家OpenID

                        // 卖家信息
                        'seller_id' => $data->seller_id ?? '',               // 卖家支付宝用户ID
                        'seller_email' => $data->seller_email ?? '',         // 卖家支付宝账号

                        // 交易时间
                        'send_pay_date' => $data->send_pay_date ?? '',       // 发送支付日期
                        'gmt_create' => $data->gmt_create ?? '',             // 交易创建时间
                        'gmt_payment' => $data->gmt_payment ?? '',           // 交易付款时间
                        'gmt_settlement' => $data->gmt_settlement ?? '',     // 交易结算时间
                        'gmt_close' => $data->gmt_close ?? '',               // 交易关闭时间

                        // 金额明细
                        'point_amount' => $data->point_amount ?? '0.00',     // 积分支付金额
                        'invoice_amount' => $data->invoice_amount ?? '0.00', // 可开发票金额
                        'discount_amount' => $data->discount_amount ?? '0.00', // 平台优惠金额
                        'mdiscount_amount' => $data->mdiscount_amount ?? '0.00', // 商家优惠金额

                        // 门店和终端信息
                        'store_id' => $data->store_id ?? '',                 // 商户门店编号
                        'store_name' => $data->store_name ?? '',             // 门店名称
                        'terminal_id' => $data->terminal_id ?? '',           // 商户机具终端编号

                        // 订单信息
                        'subject' => $data->subject ?? '',                   // 订单标题
                        'body' => $data->body ?? '',                         // 订单描述

                        // 预授权相关
                        'auth_trade_pay_mode' => $data->auth_trade_pay_mode ?? '', // 预授权支付模式

                        // 扩展信息（根据query_options返回）
                        'fund_bill_list' => $data->fund_bill_list ?? [],     // 支付金额信息
                        'voucher_detail_list' => $data->voucher_detail_list ?? [], // 优惠券详情
                        'trade_settle_info' => $data->trade_settle_info ?? [], // 交易结算信息
                        'discount_goods_detail' => $data->discount_goods_detail ?? '', // 优惠商品明细

                        // 其他信息
                        'passback_params' => $data->passback_params ?? '',   // 回传参数
                        'alipay_sub_merchant_id' => $data->alipay_sub_merchant_id ?? '', // 支付宝子商户ID
                        'industry_sepc_detail' => $data->industry_sepc_detail ?? '', // 行业特殊信息
                        'credit_pay_mode' => $data->credit_pay_mode ?? '',   // 信用支付模式
                        'credit_biz_order_id' => $data->credit_biz_order_id ?? '', // 信用业务单号
                        'hybrid_pay_mode' => $data->hybrid_pay_mode ?? '',   // 混合支付模式
                        'bkagent_resp_info' => $data->bkagent_resp_info ?? '', // 银行卡代理信息
                        'charge_amount' => $data->charge_amount ?? '0.00',   // 费用金额
                        'charge_flags' => $data->charge_flags ?? '',         // 费用标识
                        'settlement_id' => $data->settlement_id ?? '',       // 结算单号
                        'trade_goods_list' => $data->trade_goods_list ?? [], // 交易商品列表
                        'biz_settle_mode' => $data->biz_settle_mode ?? ''    // 业务结算模式
                    ]
                ];
            } else {
                // 查询失败
                return [
                    'success' => false,
                    'code' => $response->$responseNode->code ?? 'UNKNOWN_ERROR',
                    'msg' => $response->$responseNode->msg ?? '查询失败',
                    'sub_code' => $response->$responseNode->sub_code ?? '',
                    'sub_msg' => $response->$responseNode->sub_msg ?? ''
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception("交易查询失败: " . $e->getMessage());
        }
    }

    /**
     * 统一收单交易退款接口（支付宝免押退款）
     * 当交易发生之后一段时间内，由于买家或者卖家的原因需要退款时，卖家可以通过退款接口将支付款退还给买家
     *
     * @param float  $refundAmount      退款金额，单位为元，精确到小数点后两位，取值范围：[0.01,100000000](必选)
     * @param string $outTradeNo        商户订单号，与支付宝交易号trade_no不能同时为空(可选，但与trade_no必须提供其一)
     * @param string $tradeNo           支付宝交易号，与商户订单号out_trade_no不能同时为空，两者同时存在时，优先取值trade_no(可选)
     * @param string $refundReason      退款原因说明，商家自定义，将在商户和用户的pc退款账单详情中展示(可选，默认"正常退款")
     * @param string $outRequestNo      退款请求号，标识一次退款请求，需要保证在交易号下唯一(可选，如不传则自动生成)
     * @return array                    返回退款结果数组
     * @throws \Exception
     */
    public static function refundTrade($refundAmount, $outTradeNo = '', $tradeNo = '', $refundReason = '正常退款', $outRequestNo = '')
    {
        require_once EXTEND_PATH . 'alipay/aop/request/AlipayTradeRefundRequest.php';

        $client = self::getClient(); // 使用内部方法获取客户端
        $request = new \AlipayTradeRefundRequest();

        // 参数验证
        if (empty($refundAmount) || $refundAmount <= 0) {
            throw new \Exception("退款金额必须大于0");
        }

        // 金额范围验证
        if ($refundAmount < 0.01 || $refundAmount > 100000000) {
            throw new \Exception("退款金额超出范围，取值范围：[0.01,100000000]");
        }

        // 订单号验证：至少需要提供一个
        if (empty($outTradeNo) && empty($tradeNo)) {
            throw new \Exception("商户订单号(out_trade_no)和支付宝交易号(trade_no)不能同时为空，至少需要提供其中一个");
        }

        // 如果没有提供退款请求号，则自动生成一个唯一的请求号
        if (empty($outRequestNo)) {
            $outRequestNo = 'REFUND_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
        }

        // 构建业务参数
        $bizContent = [
            'refund_amount' => number_format($refundAmount, 2, '.', ''), // 退款金额，确保格式正确
            'refund_reason' => $refundReason,                            // 退款原因说明
            'out_request_no' => $outRequestNo                            // 退款请求号
        ];

        // 添加订单号参数（优先使用支付宝交易号）
        if (!empty($tradeNo)) {
            $bizContent['trade_no'] = $tradeNo;
        } elseif (!empty($outTradeNo)) {
            $bizContent['out_trade_no'] = $outTradeNo;
        }

        $request->setBizContent(json_encode($bizContent));

        try {
            $response = $client->execute($request);
            $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";

            if (!empty($response->$responseNode->code) && $response->$responseNode->code == '10000') {
                // 退款请求成功，但需要检查fund_change字段确认是否真正退款成功
                $data = $response->$responseNode;
                $fundChange = $data->fund_change ?? 'N';

                return [
                    'success' => true,
                    'code' => $data->code,
                    'msg' => $data->msg ?? '退款请求成功',
                    'fund_change' => $fundChange, // Y表示退款成功，N或无此字段需进一步确认
                    'data' => [
                        // 基本交易信息
                        'trade_no' => $data->trade_no ?? '',                 // 支付宝交易号
                        'out_trade_no' => $data->out_trade_no ?? '',         // 商户订单号
                        'buyer_logon_id' => $data->buyer_logon_id ?? '',     // 买家支付宝账号
                        'buyer_user_id' => $data->buyer_user_id ?? '',       // 买家用户ID
                        'buyer_open_id' => $data->buyer_open_id ?? '',       // 买家OpenID

                        // 退款信息
                        'refund_fee' => $data->refund_fee ?? '0.00',         // 退款金额
                        'send_back_fee' => $data->send_back_fee ?? '0.00',   // 实际退款金额
                        'gmt_refund_pay' => $data->gmt_refund_pay ?? '',     // 退款时间
                        'refund_currency' => $data->refund_currency ?? '',   // 退款币种

                        // 退款详情
                        'refund_detail_item_list' => $data->refund_detail_item_list ?? [], // 退款使用的资金渠道
                        'refund_voucher_detail_list' => $data->refund_voucher_detail_list ?? [], // 退款的券信息
                        'refund_charge_info_list' => $data->refund_charge_info_list ?? [], // 退款费用信息

                        // 门店信息
                        'store_name' => $data->store_name ?? '',             // 门店名称

                        // 其他信息
                        'refund_hyb_amount' => $data->refund_hyb_amount ?? '0.00', // 混合支付退款金额
                        'pre_auth_cancel_fee' => $data->pre_auth_cancel_fee ?? '0.00', // 预授权撤销费用
                        'present_refund_buyer_amount' => $data->present_refund_buyer_amount ?? '0.00', // 本次退款买家实退金额
                        'present_refund_discount_amount' => $data->present_refund_discount_amount ?? '0.00', // 本次退款优惠金额
                        'present_refund_mdiscount_amount' => $data->present_refund_mdiscount_amount ?? '0.00' // 本次退款商家优惠金额
                    ]
                ];
            } else {
                // 退款失败
                return [
                    'success' => false,
                    'code' => $response->$responseNode->code ?? 'UNKNOWN_ERROR',
                    'msg' => $response->$responseNode->msg ?? '退款失败',
                    'sub_code' => $response->$responseNode->sub_code ?? '',
                    'sub_msg' => $response->$responseNode->sub_msg ?? ''
                ];
            }
        } catch (\Exception $e) {
            throw new \Exception("退款请求失败: " . $e->getMessage());
        }
    }

    // ===================================================================
    //  公开验签方法
    // ===================================================================

    /**
     * 验证支付宝异步通知的签名
     * @param array $data 支付宝POST过来的所有数据 (通常是 $_POST)
     * @return bool 签名是否正确
     * @throws \Exception
     */
    public static function verifyNotify(array $data)
    {
        // 1. 获取内部已经配置好的客户端实例
        // 它已经加载了所有证书和配置，可以直接使用
        $client = self::getClient();

        // 2. 调用SDK的验签方法
        // 对于证书模式，推荐使用 rsaCheckV1 或 rsaCheckV2
        // 第一个参数是所有POST数据(回调数据)
        // 第二个参数是支付宝公钥，传 null 即可，因为 AopCertClient 内部会自动从证书中读取
        // 第三个参数是签名类型，必须与您生成签名时的一致
        $result = $client->rsaCheckV1($data, null, "RSA2");

        return $result;
    }





    // ===================================================================
    //  私有的连接器方法 (不对外暴露，仅供内部使用)
    // ===================================================================

    /**
     * 获取支付宝SDK客户端实例 (内部封装了初始化逻辑)
     * @return \AopCertClient
     * @throws \Exception
     */
    private static function getClient()
    {
        // 如果已经实例化过，直接返回，避免重复工作
        if (self::$client !== null) {
            return self::$client;
        }

        // 引入核心文件
        require_once EXTEND_PATH . 'alipay/aop/AopClient.php';
        require_once EXTEND_PATH . 'alipay/aop/AopCertClient.php';

        // 读取配置文件
        $config = Config::get('alipaychapter');
        if (empty($config)) {
            throw new \Exception("支付宝配置[alipaychapter.php]不存在或为空");
        }
        $client = new \AopCertClient();
        $client->gatewayUrl         = $config['gatewayUrl'];
        $client->appId              = $config['app_id'];
        $client->rsaPrivateKey      = $config['merchant_private_key'];
        $client->signType           = $config['sign_type'];
        $client->postCharset        = $config['charset'];

        // 设置证书路径
        $client->appCertSN          = $client->getCertSN(ROOT_PATH . $config['app_cert_path']);
        $client->alipayRootCertSN   = $client->getRootCertSN(ROOT_PATH . $config['alipay_root_cert_path']);
        $client->alipayrsaPublicKey = $client->getPublicKey(ROOT_PATH . $config['alipay_cert_path']);

        // 缓存实例并返回
        self::$client = $client;
        return self::$client;
    }
}