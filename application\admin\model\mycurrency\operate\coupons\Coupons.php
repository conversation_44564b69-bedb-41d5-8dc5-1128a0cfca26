<?php

namespace app\admin\model\mycurrency\operate\coupons;

use think\Model;
use traits\model\SoftDelete;

class Coupons extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_operate_coupons';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'discount_type_text',
        'receive_start_time_text',
        'receive_end_time_text',
        'use_expire_type_text',
        'use_start_time_text',
        'use_end_time_text',
        'status_text'
    ];
    

    
    public function getDiscountTypeList()
    {
        return ['1' => __('Discount_type 1'), '2' => __('Discount_type 2'), '3' => __('Discount_type 3')];
    }

    public function getUseExpireTypeList()
    {
        return ['1' => __('Use_expire_type 1'), '2' => __('Use_expire_type 2')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getDiscountTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['discount_type']) ? $data['discount_type'] : '');
        $list = $this->getDiscountTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getReceiveStartTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['receive_start_time']) ? $data['receive_start_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getReceiveEndTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['receive_end_time']) ? $data['receive_end_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUseExpireTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_expire_type']) ? $data['use_expire_type'] : '');
        $list = $this->getUseExpireTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getUseStartTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_start_time']) ? $data['use_start_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUseEndTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_end_time']) ? $data['use_end_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setReceiveStartTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setReceiveEndTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUseStartTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUseEndTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
