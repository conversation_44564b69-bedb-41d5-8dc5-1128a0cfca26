<?php

namespace app\admin\model\mycurrency\operate\distribution;

use think\Model;


class Moneylog extends Model
{

    

    

    // 表名
    protected $name = 'mycurrency_operate_distribution_money_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text'
    ];
    

    
    public function getTypeList()
    {
        return [
            '1' => __('Type 1'),
            '2' => __('Type 2'),
            '3' => __('Type 3'),
            '4' => __('Type 4'),
            '5' => __('Type 5'),
            '6' => __('Type 6'),
            '7' => __('Type 7'),
            '8' => __('Type 8'),
        ];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('User', 'distribution_user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function profit()
    {
        return $this->belongsTo('Profit', 'profit_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function withdrawal()
    {
        return $this->belongsTo('app\admin\model\mycurrency\operate\distribution\money\Withdrawal', 'withdrawal_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
