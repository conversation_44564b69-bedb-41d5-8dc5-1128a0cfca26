<?php

namespace app\admin\model\mycurrency\lease;

use think\Model;
use traits\model\SoftDelete;

class Order extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'mycurrency_lease_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'strategy_types_text',
        'limit_start_time_text',
        'limit_end_time_text',
        'lease_time_text',
        'return_time_text',
        'use_status_text',
        'cancell_type_text',
        'pay_status_text'
    ];
    

    
    public function getStrategyTypesList()
    {
        return ['1' => __('Strategy_types 1'), '2' => __('Strategy_types 2'), '3' => __('Strategy_types 3')];
    }

    public function getUseStatusList()
    {
        //使用状态:-1=已取消,1=申请免押中,2=支付押金中,3=支付租赁费用中【先付后用】,4=使用中,5=支付租赁费用中【先用后付or补交费用】,6=已完成
        return [
            '-1' => __('Use_status -1'),
            '1' => __('Use_status 1'),
            '2' => __('Use_status 2'),
            '3' => __('Use_status 3'),
            '4' => __('Use_status 4'),
            '5' => __('Use_status 5'),
            '6' =>  __('Use_status 6'),
        ];
    }

    public function getCancellTypeList()
    {
        return ['1' => __('Cancell_type 1'), '2' => __('Cancell_type 2')];
    }

    public function getPayStatusList()
    {
        return ['1' => __('Pay_status 1'), '2' => __('Pay_status 2')];
    }


    public function getStrategyTypesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['strategy_types']) ? $data['strategy_types'] : '');
        $list = $this->getStrategyTypesList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getLimitStartTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['limit_start_time']) ? $data['limit_start_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getLimitEndTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['limit_end_time']) ? $data['limit_end_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getLeaseTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['lease_time']) ? $data['lease_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getReturnTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['return_time']) ? $data['return_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUseStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_status']) ? $data['use_status'] : '');
        $list = $this->getUseStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getCancellTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['cancell_type']) ? $data['cancell_type'] : '');
        $list = $this->getCancellTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getPayStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_status']) ? $data['pay_status'] : '');
        $list = $this->getPayStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setLimitStartTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setLimitEndTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setLeaseTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setReturnTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function deposit()
    {
        return $this->belongsTo('Deposit', 'deposit_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function strategy()
    {
        return $this->belongsTo('Strategy', 'strategy_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function package()
    {
        return $this->belongsTo('app\admin\model\mycurrency\lease\strategy\Package', 'strategy_package_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function orderdevice()
    {
        return $this->hasOne('app\admin\model\mycurrency\lease\Orderdevice', 'lease_order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
