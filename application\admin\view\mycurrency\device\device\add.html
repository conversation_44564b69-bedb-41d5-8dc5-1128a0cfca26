<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-number" class="form-control" name="row[number]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Serial_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-serial_number" class="form-control" name="row[serial_number]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Use_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="useStatusList" item="vo"}
            <label for="row[use_status]-{$key}"><input id="row[use_status]-{$key}" name="row[use_status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_fault')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_fault" class="form-control selectpicker" name="row[is_fault]">
                {foreach name="isFaultList" item="vo"}
                    <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_online')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_online" class="form-control selectpicker" name="row[is_online]">
                {foreach name="isOnlineList" item="vo"}
                    <option value="{$key}" {in name="key" value="2"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lease_strategy_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lease_strategy_id" data-rule="required" data-source="lease/strategy/index" class="form-control selectpage" name="row[lease_strategy_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="mycurrency/merchant/store/index" class="form-control selectpage" name="row[store_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Qrcode_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-qrcode_image" class="form-control" size="50" name="row[qrcode_image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-qrcode_image" class="btn btn-danger faupload" data-input-id="c-qrcode_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-qrcode_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-qrcode_image" class="btn btn-primary fachoose" data-input-id="c-qrcode_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-qrcode_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-qrcode_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" class="form-control" name="row[title]" type="text">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
