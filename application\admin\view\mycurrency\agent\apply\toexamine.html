<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Intention')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-intention" class="form-control" name="row[intention]" type="text" value="{$row.intention|htmlentities}" readonly="readonly">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fullname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fullname" class="form-control" name="row[fullname]" type="text" value="{$row.fullname|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" class="form-control" name="row[phone]" type="text" value="{$row.phone|htmlentities}" readonly="readonly">
        </div>
    </div>
    {if condition="$row.is_identity == 1"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-gender" class="form-control" name="information[gender]" type="text" value="{$information.gender_text|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Age')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-age" class="form-control" name="information[age]" type="text" value="{$information.age|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Idnumber')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-idnumber" class="form-control" name="information[idnumber]" type="text" value="{$information.idnumber|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wechat_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wechat_number" class="form-control" name="information[wechat_number]" type="text" value="{$information.wechat_number|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mailbox')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mailbox" class="form-control" name="information[mailbox]" type="text" value="{$information.mailbox|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Intention_city_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-intention_city_id" class="form-control" name="information[intention_city_id]" type="text" value="{$information.intention_city_id|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Intention_hospital')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-intention_hospital" class="form-control" name="information[intention_hospital]" type="text" value="{$information.intention_hospital|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Idnumber_just_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <ul class="row list-inline faupload-preview" id="p-idnumber_just_image">
                <li class="col-xs-3">
                    <a href="{$information.idnumber_just_image|htmlentities}" data-url="{$information.idnumber_just_image|htmlentities}" target="_blank" class="thumbnail">
                        <img src="{$information.idnumber_just_image|htmlentities}" onerror="this.src='/juhui_sys.php/ajax/icon?suffix=jpg';this.onerror=null;" class="img-responsive">
                    </a>
                </li>
                <li class="col-xs-3">
                    <a href="{$information.idnumber_back_image|htmlentities}" data-url="{$information.idnumber_back_image|htmlentities}" target="_blank" class="thumbnail">
                        <img src="{$information.idnumber_back_image|htmlentities}" onerror="this.src='/juhui_sys.php/ajax/icon?suffix=jpg';this.onerror=null;" class="img-responsive">
                    </a>
                </li>
            </ul>
        </div>
    </div>
    {elseif condition="$row.is_identity == 2"/}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coprorate_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coprorate_name" class="form-control" name="information[coprorate_name]" type="text" value="{$information.coprorate_name|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coprorate_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coprorate_code" class="form-control" name="information[coprorate_code]" type="text" value="{$information.coprorate_code|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bus_address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bus_address" class="form-control" name="information[bus_address]" type="text" value="{$information.bus_address|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Duty_paragraph')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-duty_paragraph" class="form-control" name="information[duty_paragraph]" type="text" value="{$information.duty_paragraph|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Duty_coprorate_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-duty_coprorate_name" class="form-control" name="information[duty_coprorate_name]" type="text" value="{$information.duty_coprorate_name|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Opening_bank')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-opening_bank" class="form-control" name="information[opening_bank]" type="text" value="{$information.opening_bank|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Corporate_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-corporate_account" class="form-control" name="information[corporate_account]" type="text" value="{$information.corporate_account|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('License_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <ul class="row list-inline faupload-preview" id="p-license_image">
                <li class="col-xs-3">
                    <a href="{$information.license_image|htmlentities}" data-url="{$information.license_image|htmlentities}" target="_blank" class="thumbnail">
                        <img src="{$information.license_image|htmlentities}" onerror="this.src='/juhui_sys.php/ajax/icon?suffix=jpg';this.onerror=null;" class="img-responsive">
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Permit_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <ul class="row list-inline faupload-preview" id="p-permit_image">
                <li class="col-xs-3">
                    <a href="{$information.permit_image|htmlentities}" data-url="{$information.permit_image|htmlentities}" target="_blank" class="thumbnail">
                        <img src="{$information.permit_image|htmlentities}" onerror="this.src='/juhui_sys.php/ajax/icon?suffix=jpg';this.onerror=null;" class="img-responsive">
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Qualifications_images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <ul class="row list-inline faupload-preview" id="p-qualifications_images">
                <li class="col-xs-3">
                    <a href="{$information.qualifications_images|htmlentities}" data-url="{$information.qualifications_images|htmlentities}" target="_blank" class="thumbnail">
                        <img src="{$information.qualifications_images|htmlentities}" onerror="this.src='/juhui_sys.php/ajax/icon?suffix=jpg';this.onerror=null;" class="img-responsive">
                    </a>
                </li>
            </ul>
        </div>
    </div>
    {/if}



<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Applytime')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-applytime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[applytime]" type="text" value="{:$row.applytime?datetime($row.applytime):''}" readonly="readonly">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Business_image')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input id="c-business_image" class="form-control" size="50" name="row[business_image]" type="text" value="{$row.business_image|htmlentities}">-->
<!--                <div class="input-group-addon no-border no-padding">-->
<!--                    <span><button type="button" id="faupload-business_image" class="btn btn-danger faupload" data-input-id="c-business_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-business_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                    <span><button type="button" id="fachoose-business_image" class="btn btn-primary fachoose" data-input-id="c-business_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                </div>-->
<!--                <span class="msg-box n-right" for="c-business_image"></span>-->
<!--            </div>-->
<!--            <ul class="row list-inline faupload-preview" id="p-business_image"></ul>-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['-1'=>__('Status -1'), '2'=>__('Status 2')], $row['status'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Handle_explain')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-handle_explain" class="form-control" name="row[handle_explain]" type="text" value="{$row.handle_explain|htmlentities}">
        </div>
    </div>

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Handletime')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-handletime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[handletime]" type="text" value="{:$row.handletime?datetime($row.handletime):''}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
