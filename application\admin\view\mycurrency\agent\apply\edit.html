<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Intention')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-intention" class="form-control" name="row[intention]" type="text" value="{$row.intention|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fullname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fullname" class="form-control" name="row[fullname]" type="text" value="{$row.fullname|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" class="form-control" name="row[phone]" type="text" value="{$row.phone|htmlentities}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Applytime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-applytime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[applytime]" type="text" value="{:$row.applytime?datetime($row.applytime):''}" readonly="readonly">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Business_image')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <div class="input-group">-->
<!--                <input id="c-business_image" class="form-control" size="50" name="row[business_image]" type="text" value="{$row.business_image|htmlentities}">-->
<!--                <div class="input-group-addon no-border no-padding">-->
<!--                    <span><button type="button" id="faupload-business_image" class="btn btn-danger faupload" data-input-id="c-business_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-business_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                    <span><button type="button" id="fachoose-business_image" class="btn btn-primary fachoose" data-input-id="c-business_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                </div>-->
<!--                <span class="msg-box n-right" for="c-business_image"></span>-->
<!--            </div>-->
<!--            <ul class="row list-inline faupload-preview" id="p-business_image"></ul>-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Handle_explain')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-handle_explain" class="form-control" name="row[handle_explain]" type="text" value="{$row.handle_explain|htmlentities}">
        </div>
    </div>

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Handletime')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-handletime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[handletime]" type="text" value="{:$row.handletime?datetime($row.handletime):''}">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
