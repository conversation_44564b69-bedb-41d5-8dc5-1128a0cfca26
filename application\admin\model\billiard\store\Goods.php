<?php

namespace app\admin\model\billiard\store;

use think\Model;
use traits\model\SoftDelete;

class Goods extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'billiard_store_goods';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    







    public function store()
    {
        return $this->belongsTo('app\admin\model\mycurrency\merchant\Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function strategy()
    {
        return $this->belongsTo('app\admin\model\mycurrency\lease\Strategy', 'lease_strategy_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function goodsp()
    {
        return $this->belongsTo('app\admin\model\billiard\goods\Goods', 'goods_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
