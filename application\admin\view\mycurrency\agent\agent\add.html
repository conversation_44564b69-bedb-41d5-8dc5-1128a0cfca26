<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">-->
<!--        </div>-->
<!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Number')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-number" class="form-control" name="row[number]" type="text">-->
<!--        </div>-->
<!--    </div>-->

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fullname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fullname" class="form-control" name="row[fullname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" class="form-control" name="row[phone]" type="text">
        </div>
    </div>
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->
<!--            <input id="c-money" class="form-control" step="0.01" name="row[money]" type="number">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="form-group">-->
<!--        <label class="control-label col-xs-12 col-sm-2">{:__('Is_identity')}:</label>-->
<!--        <div class="col-xs-12 col-sm-8">-->

<!--            <div class="radio">-->
<!--                {foreach name="isidentityList" item="vo"}-->
<!--                <label for="row[is_identity]-{$key}"><input id="row[is_identity]-{$key}" name="row[is_identity]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label>-->
<!--                {/foreach}-->
<!--            </div>-->

<!--        </div>-->
<!--    </div>-->

    <!--    角色-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Role_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="roletypeList" item="vo"}
                <label for="row[role_type]-{$key}"><input id="row[is_identity]-{$key}" name="row[role_type]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group" id="level">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-level" class="form-control selectpicker" name="row[level]">
                {foreach name="levelList" item="vo"}
                <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <!--代理地区-->
    <div class="form-group" id="city">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agency_region_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'>
                <input id="c-city" class="form-control" data-toggle="city-picker" name="row[city]" type="text" value="" />
            </div>
        </div>
    </div>
<!--    分成比例-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Divideinto')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-divideinto" class="form-control" step="0.01" name="row[divideinto]" type="number">
        </div>
    </div>


    <div id="thatperson">
<!--        &lt;!&ndash;    性别&ndash;&gt;-->
<!--        <div class="form-group">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                {:build_radios('information[gender]', ['0'=>__('Gender 0'),'1'=>__('Gender 1'), '2'=>__('Gender 2')], 0)}-->
<!--            </div>-->
<!--        </div>-->
<!--        &lt;!&ndash;年龄&ndash;&gt;-->
<!--        <div class="form-group">-->
<!--            <label for="c-age" class="control-label col-xs-12 col-sm-2">{:__('Age')}:</label>-->
<!--            <div class="col-xs-12 col-sm-4">-->
<!--                <input id="c-age"  class="form-control" name="information[age]" type="number" value="">-->
<!--            </div>-->
<!--        </div>-->
<!--        &lt;!&ndash;身份证号&ndash;&gt;-->
<!--        <div class="form-group">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('Idnumber')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <input id="c-idnumber" class="form-control" name="information[idnumber]" type="text">-->
<!--            </div>-->
<!--        </div>-->

<!--        &lt;!&ndash;微信号&ndash;&gt;-->
<!--        <div class="form-group">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('Wechat_number')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <input id="c-wechat_number" class="form-control" name="information[wechat_number]" type="text">-->
<!--            </div>-->
<!--        </div>-->

<!--        &lt;!&ndash;邮箱&ndash;&gt;-->
<!--        <div class="form-group">-->
<!--            <label class="control-label col-xs-12 col-sm-2">{:__('Mailbox')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <input id="c-mailbox" class="form-control" name="information[mailbox]" type="text">-->
<!--            </div>-->
<!--        </div>-->

<!--        &lt;!&ndash;身份证正面照片&ndash;&gt;-->
<!--        <div class="form-group">-->
<!--            <label for="c-idnumber_just_image" class="control-label col-xs-12 col-sm-2">{:__('Idnumber_just_image')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <div class="input-group">-->
<!--                    <input id="c-idnumber_just_image" data-rule="" class="form-control" size="50" name="information[idnumber_just_image]" type="text" value="">-->
<!--                    <div class="input-group-addon no-border no-padding">-->
<!--                        <span><button type="button" id="faupload-idnumber_just_image" class="btn btn-danger faupload" data-input-id="c-idnumber_just_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-idnumber_just_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                        <span><button type="button" id="fachoose-idnumber_just_image" class="btn btn-primary fachoose" data-input-id="c-idnumber_just_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                    </div>-->
<!--                    <span class="msg-box n-right" for="c-idnumber_just_image"></span>-->
<!--                </div>-->
<!--                <ul class="row list-inline faupload-preview" id="p-idnumber_just_image"></ul>-->
<!--            </div>-->
<!--        </div>-->

<!--        &lt;!&ndash;身份证反面照片&ndash;&gt;-->
<!--        <div class="form-group">-->
<!--            <label for="c-idnumber_back_image" class="control-label col-xs-12 col-sm-2">{:__('Idnumber_back_image')}:</label>-->
<!--            <div class="col-xs-12 col-sm-8">-->
<!--                <div class="input-group">-->
<!--                    <input id="c-idnumber_back_image" data-rule="" class="form-control" size="50" name="information[idnumber_back_image]" type="text" value="">-->
<!--                    <div class="input-group-addon no-border no-padding">-->
<!--                        <span><button type="button" id="faupload-idnumber_back_image" class="btn btn-danger faupload" data-input-id="c-idnumber_back_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-idnumber_back_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>-->
<!--                        <span><button type="button" id="fachoose-idnumber_back_image" class="btn btn-primary fachoose" data-input-id="c-idnumber_back_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>-->
<!--                    </div>-->
<!--                    <span class="msg-box n-right" for="c-idnumber_back_image"></span>-->
<!--                </div>-->
<!--                <ul class="row list-inline faupload-preview" id="p-idnumber_back_image"></ul>-->
<!--            </div>-->
<!--        </div>-->
    </div>
    <div id="company">
        <!--公司名称-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Coprorate_name')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-coprorate_name" class="form-control" name="information[coprorate_name]" type="text">
            </div>
        </div>

        <!--公司代码-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Coprorate_code')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-coprorate_code" class="form-control" name="information[coprorate_code]" type="text">
            </div>
        </div>

        <!--营业地址-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Bus_address')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-bus_address" class="form-control" name="information[bus_address]" type="text">
            </div>
        </div>

        <!--税票号-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Duty_paragraph')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-duty_paragraph" class="form-control" name="information[duty_paragraph]" type="text">
            </div>
        </div>

        <!--税票公司名称-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Duty_coprorate_name')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-duty_coprorate_name" class="form-control" name="information[duty_coprorate_name]" type="text">
            </div>
        </div>

        <!--开户行-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Opening_bank')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-opening_bank" class="form-control" name="information[opening_bank]" type="text">
            </div>
        </div>

        <!--对公账户-->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Corporate_account')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-corporate_account" class="form-control" name="information[corporate_account]" type="text">
            </div>
        </div>
<!--        营业执照-->
        <div class="form-group">
            <label for="c-license_image" class="control-label col-xs-12 col-sm-2">{:__('License_image')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-license_image" data-rule="" class="form-control" size="50" name="information[license_image]" type="text" value="">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-license_image" class="btn btn-danger faupload" data-input-id="c-license_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-license_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-license_image" class="btn btn-primary fachoose" data-input-id="c-license_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-license_image"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-license_image"></ul>
            </div>
        </div>
        <!--开户许可-->
        <div class="form-group">
            <label for="c-permit_image" class="control-label col-xs-12 col-sm-2">{:__('Permit_image')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-permit_image" data-rule="" class="form-control" size="50" name="information[permit_image]" type="text" value="">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-permit_image" class="btn btn-danger faupload" data-input-id="c-permit_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-permit_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-permit_image" class="btn btn-primary fachoose" data-input-id="c-permit_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-permit_image"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-permit_image"></ul>
            </div>
        </div>
        <!--资质证明-->
        <div class="form-group">
            <label for="c-qualifications_images" class="control-label col-xs-12 col-sm-2">{:__('Qualifications_images')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-qualifications_images" data-rule="" class="form-control" size="50" name="information[qualifications_images]" type="text" value="">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-qualifications_images" class="btn btn-danger faupload" data-input-id="c-qualifications_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-qualifications_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-qualifications_images" class="btn btn-primary fachoose" data-input-id="c-qualifications_images" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-qualifications_images"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-qualifications_images"></ul>
            </div>
        </div>
    </div>



    <div class="form-group">
        <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-username" data-rule="required" class="form-control" name="admin[username]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="password" class="form-control" name="admin[password]" type="password" value="" placeholder="" autocomplete="new-password" />
        </div>
    </div>



    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
